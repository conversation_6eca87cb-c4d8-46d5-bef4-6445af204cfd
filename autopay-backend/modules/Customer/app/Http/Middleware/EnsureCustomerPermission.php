<?php

namespace Modules\Customer\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class EnsureCustomerPermission
{
    /**
     * Handle an incoming request.
     *
     * @param  Closure(Request): (Response)  $next
     */
    public function handle(Request $request, Closure $next, string $permission): Response
    {
        $customer = Auth::guard('customer')->user();
        $organization = app('current.organization');

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'Chưa đăng nhập.',
            ], 401);
        }

        if (!$organization || $customer->organization_id !== $organization->id) {
            return response()->json([
                'success' => false,
                'message' => 'Không có quyền truy cập tổ chức này.',
            ], 403);
        }

        // Check if customer has the required permission
        if (!$customer->hasPermissionTo($permission, 'customer')) {
            return response()->json([
                'success' => false,
                'message' => '<PERSON>hông có quyền thực hiện hành động này.',
                'required_permission' => $permission,
            ], 403);
        }

        return $next($request);
    }
}

<?php

namespace Modules\Customer\Models;

use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use LucasDotVin\Soulbscription\Models\Concerns\HasSubscriptions;
use Modules\Organization\Models\Organization;
use Spatie\Permission\Traits\HasRoles;

class Customer extends Authenticatable
{
    use HasApiTokens, Notifiable, HasUlids, HasSubscriptions, HasRoles;

    /**
     * The guard name for Spatie Permissions
     */
    protected $guard_name = 'customer';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'organization_id',
        'first_name',
        'last_name',
        'email',
        'phone',
        'password',
        'email_verified_at',
        'is_active',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Get the organization that owns the customer.
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Get the customer's full name.
     */
    public function getFullNameAttribute(): string
    {
        return trim($this->first_name . ' ' . $this->last_name);
    }



    /**
     * Scope a query to only include active customers.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include customers for a specific organization.
     */
    public function scopeForOrganization($query, $organizationId)
    {
        return $query->where('organization_id', $organizationId);
    }

    /**
     * Get teammates that this customer has added
     */
    public function teammates(): BelongsToMany
    {
        return $this->belongsToMany(
            Customer::class,
            'customer_teammates',
            'customer_id',
            'teammate_customer_id'
        )->withPivot(['is_active', 'created_at'])
         ->withTimestamps();
    }

    /**
     * Get customers who have added this customer as teammate
     */
    public function primaryCustomers(): BelongsToMany
    {
        return $this->belongsToMany(
            Customer::class,
            'customer_teammates',
            'teammate_customer_id',
            'customer_id'
        )->withPivot(['is_active', 'created_at'])
         ->withTimestamps();
    }

    /**
     * Add a teammate with specific role
     */
    public function addTeammate(Customer $teammate, string $role): bool
    {
        // Check if both customers are in the same organization
        if ($this->organization_id !== $teammate->organization_id) {
            return false;
        }

        // Check if teammate relationship already exists
        if ($this->teammates()->where('teammate_customer_id', $teammate->id)->exists()) {
            return false;
        }

        // Add teammate relationship
        $this->teammates()->attach($teammate->id, [
            'is_active' => true,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Assign role to teammate using organization as team context
        $teammate->assignRole($role, $this->organization_id);

        return true;
    }

    /**
     * Remove a teammate
     */
    public function removeTeammate(Customer $teammate): bool
    {
        // Remove teammate relationship
        $removed = $this->teammates()->detach($teammate->id);

        if ($removed) {
            // Remove all roles for this teammate in this organization context
            $teammate->removeRole($teammate->getRoleNames()->toArray(), $this->organization_id);
        }

        return $removed > 0;
    }

    /**
     * Check if this customer has a specific teammate
     */
    public function hasTeammate(Customer $teammate): bool
    {
        return $this->teammates()
            ->where('teammate_customer_id', $teammate->id)
            ->where('customer_teammates.is_active', true)
            ->exists();
    }

    /**
     * Check if this customer can perform action on behalf of another customer
     */
    public function canActFor(Customer $primaryCustomer, string $permission): bool
    {
        // Check if this customer is a teammate of the primary customer
        if (!$primaryCustomer->hasTeammate($this)) {
            return false;
        }

        // Check permission using organization as team context
        return $this->hasPermissionTo($permission, 'customer');
    }

    /**
     * Get all permissions this customer has been granted by primary customers
     */
    public function getTeammatePermissions(): array
    {
        $permissions = [];

        foreach ($this->primaryCustomers as $primaryCustomer) {
            $customerPermissions = $this->getPermissionsViaRoles()
                ->where('guard_name', 'customer')
                ->pluck('name')
                ->toArray();

            $permissions[$primaryCustomer->id] = $customerPermissions;
        }

        return $permissions;
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_teammates', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->foreignUlid('customer_id')->constrained('customers')->onDelete('cascade');
            $table->foreignUlid('teammate_customer_id')->constrained('customers')->onDelete('cascade');
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            // Unique constraint: a customer can only add another customer as teammate once
            $table->unique(['customer_id', 'teammate_customer_id']);

            // Index for performance
            $table->index(['customer_id', 'is_active']);
            $table->index(['teammate_customer_id', 'is_active']);

            // Note: Self-referencing prevention will be handled in application logic
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_teammates');
    }
};

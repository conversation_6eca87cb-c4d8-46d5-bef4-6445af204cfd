<?php

namespace Modules\Customer\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\User\Models\Permission;
use Modules\User\Models\Role;

class CustomerPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create customer permissions
        $permissions = [
            // Financial permissions
            'customer:balance:view' => 'Xem số dư tài khoản',
            'customer:amount:view' => 'Xem số tiền trong giao dịch',
            'customer:transactions:view' => 'Xem lịch sử giao dịch',
            'customer:transactions:create' => 'Tạo giao dịch mới',
            'customer:payments:view' => 'Xem thanh toán',
            'customer:payments:create' => 'Tạo thanh toán',
            'customer:payments:approve' => 'Duyệt thanh toán',

            // Account permissions
            'customer:bank_accounts:view' => 'Xem thông tin tài khoản ngân hàng',
            'customer:bank_accounts:manage' => 'Quản lý tài khoản ngân hàng',
            'customer:invoices:view' => 'Xem hóa đơn',
            'customer:invoices:create' => 'Tạo hóa đơn',
            'customer:invoices:edit' => 'Chỉnh sửa hóa đơn',

            // Report permissions
            'customer:reports:view' => 'Xem báo cáo',
            'customer:reports:export' => 'Xuất báo cáo',
            'customer:analytics:view' => 'Xem phân tích',

            // Profile permissions
            'customer:profile:view' => 'Xem thông tin cá nhân',
            'customer:profile:edit' => 'Chỉnh sửa thông tin cá nhân',

            // Teammate management permissions
            'customer:teammates:view' => 'Xem danh sách teammate',
            'customer:teammates:manage' => 'Quản lý teammates',
        ];

        foreach ($permissions as $name => $description) {
            Permission::firstOrCreate([
                'name' => $name,
                'guard_name' => 'customer',
            ]);
        }

        // Create customer roles
        $roles = [
            'customer_viewer' => [
                'name' => 'Customer Viewer',
                'description' => 'Chỉ xem thông tin cơ bản',
                'permissions' => [
                    'customer:balance:view',
                    'customer:transactions:view',
                    'customer:invoices:view',
                    'customer:reports:view',
                    'customer:profile:view',
                ]
            ],
            'customer_operator' => [
                'name' => 'Customer Operator',
                'description' => 'Xem và tạo giao dịch',
                'permissions' => [
                    'customer:balance:view',
                    'customer:amount:view',
                    'customer:transactions:view',
                    'customer:transactions:create',
                    'customer:payments:view',
                    'customer:payments:create',
                    'customer:invoices:view',
                    'customer:invoices:create',
                    'customer:reports:view',
                    'customer:profile:view',
                    'customer:profile:edit',
                ]
            ],
            'customer_manager' => [
                'name' => 'Customer Manager',
                'description' => 'Quản lý hầu hết tính năng',
                'permissions' => [
                    'customer:balance:view',
                    'customer:amount:view',
                    'customer:transactions:view',
                    'customer:transactions:create',
                    'customer:payments:view',
                    'customer:payments:create',
                    'customer:payments:approve',
                    'customer:bank_accounts:view',
                    'customer:bank_accounts:manage',
                    'customer:invoices:view',
                    'customer:invoices:create',
                    'customer:invoices:edit',
                    'customer:reports:view',
                    'customer:reports:export',
                    'customer:analytics:view',
                    'customer:profile:view',
                    'customer:profile:edit',
                    'customer:teammates:view',
                ]
            ],
            'customer_admin' => [
                'name' => 'Customer Admin',
                'description' => 'Toàn quyền như primary customer',
                'permissions' => array_keys($permissions), // All permissions
            ],
        ];

        foreach ($roles as $roleName => $roleData) {
            $role = Role::firstOrCreate([
                'name' => $roleName,
                'guard_name' => 'customer',
            ], [
                'description' => $roleData['description'],
            ]);

            // Assign permissions to role
            $role->syncPermissions($roleData['permissions']);
        }

        $this->command->info('Customer permissions and roles created successfully!');
    }
}
